@model XQ360.DataMigration.Web.Models.BulkSeederViewModel

<div class="driver-count-input">
    <div class="mb-3">
        <input type="number" 
               id="driverCount" 
               name="DriverCount"
               class="form-control" 
               value="@Model.DriverCount"
               min="1"
               max="1000000"
               placeholder="Enter driver count"
               required>
        
        <div id="driver-validation-error" class="invalid-feedback" style="display: none;"></div>
        
        <div class="form-text d-flex justify-content-between align-items-center">
            <span id="driver-environment-limit" class="text-warning" style="display: none;">
                <i class="fas fa-exclamation-triangle me-1"></i>
                Environment limit: <span id="driver-limit-value">0</span>
            </span>
        </div>
    </div>





    <!-- Large Volume Warning -->
    <div id="driver-large-volume-warning" class="alert alert-warning" style="display: none;">
        <div class="d-flex align-items-start">
            <i class="fas fa-exclamation-triangle me-2 mt-1"></i>
            <div class="flex-grow-1">
                <div class="fw-bold">Large Volume Operation</div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="driver-large-volume-acknowledged">
                    <label class="form-check-label small" for="driver-large-volume-acknowledged">
                        I understand this is a large volume operation
                    </label>
                </div>
            </div>
        </div>
    </div>


</div>

<script>
    const driverCountConfig = {
        minCount: 1,
        maxCount: 1000000,
        largeVolumeThreshold: 75000,
        environmentLimit: null,
        optimalRatioMin: 1.2, // 1.2 drivers per vehicle
        optimalRatioMax: 2.0   // 2.0 drivers per vehicle
    };

    function initializeDriverCountInput() {
        // Ensure jQuery is available
        if (typeof $ === 'undefined') {
            console.error('jQuery is not loaded - driver count input cannot initialize');
            return;
        }
        // Initialize driver count validation
        initializeDriverCountValidation();
        
        // Handle input changes
        $('#driverCount').on('input', function() {
            validateDriverCount();
            updateEstimatedTime();
        });

        $('#driverCount').on('blur', function() {
            validateDriverCount();
        });

        // Handle large volume acknowledgment
        $('#driver-large-volume-acknowledged').on('change', function() {
            validateDriverCount();
        });

        // Initialize with existing value
        if ($('#driverCount').val()) {
            validateDriverCount();
            updateEstimatedTime();
        }
    }

    // Initialize when DOM and scripts are ready
    if (typeof $ !== 'undefined') {
        $(document).ready(function() {
            initializeDriverCountInput();
        });
    } else {
        // Fallback for when jQuery isn't loaded yet
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof $ !== 'undefined') {
                initializeDriverCountInput();
            } else {
                // Retry after a short delay
                setTimeout(function() {
                    if (typeof $ !== 'undefined') {
                        initializeDriverCountInput();
                    }
                }, 100);
            }
        });
    }

    function initializeDriverCountValidation() {
        $('#driver-min-count').text(driverCountConfig.minCount.toLocaleString());
        $('#driver-max-count').text(driverCountConfig.maxCount.toLocaleString());
        
        // TODO: Load environment limits from server
        // loadEnvironmentLimits();
    }

    function validateDriverCount() {
        const input = $('#driverCount');
        const value = parseInt(input.val()) || 0;
        let isValid = true;
        let validationMessage = '';
        let validationTitle = 'Valid Range';
        let validationIcon = '<i class="fas fa-check-circle text-success"></i>';

        // Clear previous validation states
        input.removeClass('is-invalid');
        $('#driver-validation-error').hide();
        $('#driver-large-volume-warning').hide();

        if (value <= 0) {
            isValid = false;
            validationMessage = 'Driver count must be greater than 0';
            validationTitle = 'Invalid Count';
            validationIcon = '<i class="fas fa-exclamation-circle text-danger"></i>';
        } else if (value < driverCountConfig.minCount) {
            isValid = false;
            validationMessage = `Minimum driver count is ${driverCountConfig.minCount.toLocaleString()}`;
            validationTitle = 'Below Minimum';
            validationIcon = '<i class="fas fa-exclamation-triangle text-warning"></i>';
        } else if (value > driverCountConfig.maxCount) {
            isValid = false;
            validationMessage = `Maximum driver count is ${driverCountConfig.maxCount.toLocaleString()}`;
            validationTitle = 'Above Maximum';
            validationIcon = '<i class="fas fa-exclamation-circle text-danger"></i>';
        } else if (driverCountConfig.environmentLimit && value > driverCountConfig.environmentLimit) {
            isValid = false;
            validationMessage = `Environment limit is ${driverCountConfig.environmentLimit.toLocaleString()} drivers`;
            validationTitle = 'Environment Limit Exceeded';
            validationIcon = '<i class="fas fa-exclamation-triangle text-warning"></i>';
        } else if (value >= driverCountConfig.largeVolumeThreshold) {
            // Large volume - check acknowledgment
            $('#driver-large-volume-warning').show();
            
            if (!$('#driver-large-volume-acknowledged').is(':checked')) {
                isValid = false;
                validationMessage = 'Please acknowledge the large volume operation';
                validationTitle = 'Acknowledgment Required';
                validationIcon = '<i class="fas fa-exclamation-triangle text-warning"></i>';
            } else {
                validationMessage = 'Large volume operation acknowledged';
                validationTitle = 'Large Volume';
                validationIcon = '<i class="fas fa-check-circle text-success"></i>';
            }
        }

        // Show/hide validation feedback
        if (value > 0) {
            $('#driver-validation-feedback').show();
            $('#driver-requirements').show();
        } else {
            $('#driver-validation-feedback').hide();
            $('#driver-requirements').hide();
        }

        // Update validation display
        $('#driver-validation-icon').html(validationIcon);
        $('#driver-validation-title').text(validationTitle);
        $('#driver-validation-message').text(validationMessage);

        // Show error state if invalid
        if (!isValid && value > 0) {
            input.addClass('is-invalid');
            $('#driver-validation-error').text(validationMessage).show();
        }

        // Update field validation state
        updateFieldValidation('driverCount', isValid);

        return isValid;
    }



    function updateEstimatedTime() {
        const driverCount = parseInt($('#driverCount').val()) || 0;
        
        if (driverCount <= 0) {
            $('#driver-estimated-time').text('N/A');
            return;
        }

        // Rough estimation: 200 drivers per minute
        const driverMinutes = Math.ceil(driverCount / 200);
        const totalMinutes = driverMinutes + 1; // Add setup time

        let timeText;
        if (totalMinutes < 60) {
            timeText = `~${totalMinutes} minute${totalMinutes !== 1 ? 's' : ''}`;
        } else {
            const hours = Math.floor(totalMinutes / 60);
            const remainingMinutes = totalMinutes % 60;
            timeText = `~${hours}h ${remainingMinutes}m`;
        }

        $('#driver-estimated-time').text(timeText);
    }

    function updateDriverVehicleRatio() {
        const driverCount = parseInt($('#driverCount').val()) || 0;
        const vehicleCount = parseInt($('#vehicleCount').val()) || 0;

        if (driverCount <= 0 || vehicleCount <= 0) {
            $('#driver-vehicle-ratio-warning').hide();
            return;
        }

        const ratio = driverCount / vehicleCount;
        $('#current-ratio').text(`${ratio.toFixed(1)}:1 (${driverCount.toLocaleString()} drivers : ${vehicleCount.toLocaleString()} vehicles)`);

        let recommendationText = '';
        let alertClass = 'alert-info';

        if (ratio < driverCountConfig.optimalRatioMin) {
            recommendationText = 'Consider adding more drivers - typically 1.2-2.0 drivers per vehicle is optimal';
            alertClass = 'alert-warning';
        } else if (ratio > driverCountConfig.optimalRatioMax) {
            recommendationText = 'You have many drivers per vehicle - this is acceptable but may indicate over-staffing';
            alertClass = 'alert-info';
        } else {
            recommendationText = 'Good driver-to-vehicle ratio';
            alertClass = 'alert-success';
        }

        $('#ratio-recommendation').text(recommendationText);
        $('#driver-vehicle-ratio-warning')
            .removeClass('alert-info alert-warning alert-success')
            .addClass(alertClass)
            .show();
    }

    function loadEnvironmentLimits() {
        // TODO: Implement when environment service is available
        $.ajax({
            url: '/api/environment/limits',
            method: 'GET',
            success: function(limits) {
                if (limits.driverLimit) {
                    driverCountConfig.environmentLimit = limits.driverLimit;
                    $('#driver-limit-value').text(limits.driverLimit.toLocaleString());
                    $('#driver-environment-limit').show();
                }
            },
            error: function() {
                // Ignore errors for now
            }
        });
    }

    // Export for use by other components
    window.getDriverCount = function() {
        return parseInt($('#driverCount').val()) || 0;
    };

    window.isDriverCountValid = function() {
        return validateDriverCount();
    };
</script>
