@model XQ360.DataMigration.Web.Models.BulkSeederViewModel
@{
    ViewData["Title"] = "Data Seeder";
}

<div class="container-fluid py-4">
    <div class="row">
        <!-- Main Form Column -->
        <div class="col-lg-8">
            <!-- Import Progress Tracker (shown when import is active) -->
            @if (!string.IsNullOrEmpty(Model.ActiveSessionId))
            {
                <div class="mb-4">
                    <div id="progressTracker" class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-spinner fa-spin me-2"></i>
                                Seeding Operation in Progress
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="progress mb-3">
                                <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                                     role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                    0%
                                </div>
                            </div>
                            <div id="progressStatus" class="text-muted">Initializing...</div>
                            <div class="mt-3">
                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="cancelSeeding()">
                                    <i class="fas fa-stop me-1"></i>
                                    Cancel Operation
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            }

            <!-- Single-Page Seeder Form -->
            <div class="card minimal-card seeder-form-card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-database me-2"></i>
                        Bulk Data Seeder Configuration
                    </h4>
                </div>
                
                <div class="card-body">
                    <form id="seederForm" asp-action="CreateSession" asp-controller="Seeder" method="post">
                        <!-- Environment Selection -->
                        <div class="form-section mb-4">
                            <div class="section-header mb-3">
                                <h5 class="section-title">
                                    <i class="fas fa-server me-2"></i>
                                    Environment
                                </h5>
                            </div>

                            <div class="row">
                                <div class="col-md-8">
                                    <div class="form-group">
                                        <select asp-for="SelectedEnvironment" class="form-select" id="environmentSelect">
                                            <option value="">Select an environment...</option>
                                            @foreach (var env in Model.AvailableEnvironments)
                                            {
                                                <option value="@env.Key" data-description="@env.Description" 
                                                        selected="@(env.Key == Model.SelectedEnvironment)">
                                                    @env.DisplayName
                                                </option>
                                            }
                                        </select>
                                        <span asp-validation-for="SelectedEnvironment" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="environment-status mt-4 pt-2">
                                        <div id="currentEnvironmentStatus" class="alert alert-success alert-sm">
                                            <i class="fas fa-check-circle me-1"></i>
                                            <small><strong>Current:</strong> @Model.CurrentEnvironment</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div id="environmentDescription" class="mt-2" style="display: none;">
                                <div class="alert alert-info alert-sm">
                                    <i class="fas fa-info-circle me-1"></i>
                                    <small id="environmentDescriptionText"></small>
                                </div>
                            </div>

                            <div class="mt-2">
                                <button type="button" id="switchEnvironmentBtn" class="btn btn-outline-primary btn-sm" style="display: none;">
                                    <i class="fas fa-exchange-alt me-1"></i>
                                    Switch to Selected Environment
                                </button>
                            </div>
                        </div>

                        <!-- Dealer Selection Section -->
                        <div class="form-section mb-4">
                            <div class="section-header mb-3">
                                <h5 class="section-title">
                                    <i class="fas fa-building me-2"></i>
                                    Dealer
                                </h5>
                            </div>

                            @await Html.PartialAsync("_DealerSelector", Model)
                        </div>

                        <!-- Customer Selection Section -->
                        <div class="form-section mb-4">
                            <div class="section-header mb-3">
                                <h5 class="section-title">
                                    <i class="fas fa-users me-2"></i>
                                    Customer
                                </h5>
                            </div>

                            @await Html.PartialAsync("_CustomerSelector", Model)
                        </div>

                        <!-- Vehicle Count Section -->
                        <div class="form-section mb-4">
                            <div class="section-header mb-3">
                                <h5 class="section-title">
                                    <i class="fas fa-car me-2"></i>
                                    Vehicle Count
                                </h5>
                            </div>

                            @await Html.PartialAsync("_VehicleCountInput", Model)
                        </div>

                        <!-- Driver Count Section -->
                        <div class="form-section mb-4">
                            <div class="section-header mb-3">
                                <h5 class="section-title">
                                    <i class="fas fa-id-card me-2"></i>
                                    Driver Count
                                </h5>
                            </div>

                            @await Html.PartialAsync("_DriverCountInput", Model)
                        </div>

                        <!-- Form Actions -->
                        <div class="form-section">
                            <div class="d-flex justify-content-between align-items-center">
                                <button type="button" class="btn btn-outline-secondary" onclick="validateConfiguration()">
                                    <i class="fas fa-check-circle me-1"></i>
                                    Validate Configuration
                                </button>
                                
                                <button type="submit" class="btn btn-primary" id="startSeedingBtn" disabled>
                                    <i class="fas fa-play me-1"></i>
                                    Start Seeding Operation
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Configuration Summary Sidebar -->
        <div class="col-lg-4">
            <div class="sticky-top" style="top: 1rem;">
                <!-- Configuration Summary -->
                <div class="card summary-card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list-ul me-2"></i>
                            Configuration Summary
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="configurationSummary">
                            <!-- Complete Configuration -->
                            <div id="completeSummary" style="display: none;">
                                <div class="d-flex align-items-center mb-3">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <div>
                                        <div class="fw-bold">Ready to Seed</div>
                                        <div class="small text-muted">All required fields completed</div>
                                    </div>
                                </div>
                                
                                <div class="summary-section mb-3">
                                    <h6 class="summary-label">Environment</h6>
                                    <div class="summary-value" id="summaryEnvironment">@Model.CurrentEnvironment</div>
                                </div>
                                
                                <div class="summary-section mb-3">
                                    <h6 class="summary-label">Dealer</h6>
                                    <div class="summary-value" id="summaryDealer">Not selected</div>
                                </div>
                                
                                <div class="summary-section mb-3">
                                    <h6 class="summary-label">Customer</h6>
                                    <div class="summary-value" id="summaryCustomer">Not selected</div>
                                </div>
                                
                                <div class="summary-section mb-3">
                                    <h6 class="summary-label">Data to Generate</h6>
                                    <div class="summary-value">
                                        <div id="summaryVehicles">0 vehicles</div>
                                        <div id="summaryDrivers">0 drivers</div>
                                    </div>
                                </div>
                                
                                <div class="summary-section mb-3">
                                    <h6 class="summary-label">Estimated Time</h6>
                                    <div class="summary-value" id="summaryTime">Calculating...</div>
                                </div>

                                <!-- Validation Results -->
                                <div id="validationResults" style="display: none;">
                                    <div class="summary-section">
                                        <h6 class="summary-label">Pre-Validation</h6>
                                        <div id="validationStatus" class="summary-value"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Incomplete Form -->
                            <div id="incompleteSummary">
                                <div class="alert alert-light">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <div>
                                            <div class="fw-bold">Complete Configuration</div>
                                            <div class="small text-muted">Fill out all required fields to validate</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/seeder-wizard.js" asp-append-version="true"></script>
    <script>
        $(document).ready(function() {
            // Ensure SeederWizard is available before initializing
            if (typeof SeederWizard !== 'undefined') {
                // Initialize seeder wizard
                SeederWizard.init({
                    sessionId: '@Model.ActiveSessionId',
                    isImporting: @Html.Raw(Model.IsImporting.ToString().ToLower()),
                    currentEnvironment: '@Model.CurrentEnvironment'
                });
            } else {
                console.error('SeederWizard is not defined - check script loading order');
            }
        });
    </script>
}
