@model XQ360.DataMigration.Web.Models.BulkSeederViewModel

<div class="dealer-selector">
    <div class="mb-3">
        <input type="text" id="dealer-search" class="form-control"
            placeholder="Search for dealer by name or subdomain..." autocomplete="off">

        <div id="dealer-validation-error" class="invalid-feedback" style="display: none;"></div>

        <div id="dealer-loading" class="form-text" style="display: none;">
            <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
            Searching dealers...
        </div>
    </div>

    <!-- Search Results Dropdown -->
    <div id="dealer-results" class="dropdown-menu w-100" style="display: none; max-height: 300px; overflow-y: auto;">
        <!-- Dynamic results will be populated here -->
    </div>

    <!-- Selected Dealer Display -->
    <div id="selected-dealer" class="card border-success" style="display: none;">
        <div class="card-header bg-success text-white">
            <h6 class="card-title mb-0">
                <i class="fas fa-building me-2"></i>
                Selected Dealer
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <div class="dealer-info">
                        <div class="fw-bold" id="dealer-name"></div>
                        <div class="text-muted small" id="dealer-subdomain"></div>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <span class="badge bg-success">
                        <i class="fas fa-check me-1"></i>
                        Active
                    </span>
                </div>
            </div>

            <div class="dealer-actions mt-2 pt-2 border-top">
                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearDealerSelection()">
                    <i class="fas fa-times me-1"></i>
                    Change Dealer
                </button>
            </div>
        </div>
    </div>

    <!-- No Results Message -->
    <div id="no-dealer-results" class="alert alert-warning" style="display: none;">
        <div class="d-flex align-items-center">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <div>
                <strong>No dealers found</strong>
                <br>
                <small>Try a different search term or check the dealer name/subdomain</small>
            </div>
        </div>
    </div>
    <!-- Error Message -->
    <div id="dealer-error" class="alert alert-danger" style="display: none;">
        <div class="d-flex align-items-center">
            <i class="fas fa-exclamation-circle me-2"></i>
            <div>
                <strong>Error loading dealers</strong>
                <br>
                <small id="dealer-error-message">An error occurred while searching for dealers</small>
            </div>
        </div>
    </div>

    <!-- Hidden input to store selected dealer ID -->
    <input type="hidden" id="selected-dealer-id" name="SelectedDealer.Id" value="@Model.SelectedDealer?.Id" />
    <input type="hidden" id="selected-dealer-name" name="SelectedDealer.Name" value="@Model.SelectedDealer?.Name" />
    <input type="hidden" id="selected-dealer-subdomain" name="SelectedDealer.Subdomain"
        value="@Model.SelectedDealer?.Subdomain" />
</div>

<script>
    // Dealer selector functionality
    let dealerSearchTimeout;
    let selectedDealer = @Html.Raw(Model.SelectedDealer != null ?
                $"{{id: '{Model.SelectedDealer.Id}', name: '{Model.SelectedDealer.Name}', subdomain: '{Model.SelectedDealer.Subdomain}'}}" :
                "null");

    function initializeDealerSelector() {
        // Ensure jQuery is available
        if (typeof $ === 'undefined') {
            console.error('jQuery is not loaded - dealer selector cannot initialize');
            return;
        }
        // Initialize with existing selection if available
        if (selectedDealer) {
            displaySelectedDealer(selectedDealer);
        }

        // Handle search input
        $('#dealer-search').on('input', function () {
            const query = $(this).val().trim();

            if (query.length < 2) {
                hideDealerResults();
                return;
            }

            // Debounce search
            clearTimeout(dealerSearchTimeout);
            dealerSearchTimeout = setTimeout(() => {
                searchDealers(query);
            }, 300);
        });

        // Handle search focus
        $('#dealer-search').on('focus', function () {
            const query = $(this).val().trim();
            if (query.length >= 2) {
                searchDealers(query);
            }
        });

        // Handle click outside to hide results
        $(document).on('click', function (e) {
            if (!$(e.target).closest('.dealer-selector').length) {
                hideDealerResults();
            }
        });
    }

    // Initialize when DOM and scripts are ready
    if (typeof $ !== 'undefined') {
        $(document).ready(function () {
            initializeDealerSelector();
        });
    } else {
        // Fallback for when jQuery isn't loaded yet
        document.addEventListener('DOMContentLoaded', function () {
            if (typeof $ !== 'undefined') {
                initializeDealerSelector();
            } else {
                // Retry after a short delay
                setTimeout(function () {
                    if (typeof $ !== 'undefined') {
                        initializeDealerSelector();
                    }
                }, 100);
            }
        });
    }

    function searchDealers(query) {
        showDealerLoading();
        hideDealerError();

        $.ajax({
            url: '/api/dealers/search',
            method: 'GET',
            data: { query: query },
            success: function (dealers) {
                hideDealerLoading();
                displayDealerResults(dealers);
            },
            error: function (xhr, status, error) {
                hideDealerLoading();
                showDealerError('Failed to search dealers: ' + (xhr.responseJSON?.message || error));
            }
        });
    }

    function displayDealerResults(dealers) {
        const resultsContainer = $('#dealer-results');

        if (dealers.length === 0) {
            $('#no-dealer-results').show();
            resultsContainer.hide();
            return;
        }

        $('#no-dealer-results').hide();

        let html = '';
        dealers.forEach(dealer => {
            html += `
                <a href="#" class="dropdown-item" onclick="selectDealer('${dealer.id}', '${dealer.name}', '${dealer.subdomain}')">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="fw-bold">${dealer.name}</div>
                            <div class="text-muted small">${dealer.subdomain}</div>
                        </div>
                        <span class="badge bg-${dealer.isActive ? 'success' : 'secondary'}">
                            ${dealer.isActive ? 'Active' : 'Inactive'}
                        </span>
                    </div>
                </a>`;
        });

        resultsContainer.html(html).show();
    }

    function selectDealer(id, name, subdomain) {
        selectedDealer = { id, name, subdomain };
        displaySelectedDealer(selectedDealer);
        hideDealerResults();
        $('#dealer-search').val('');

        // Update hidden inputs
        $('#selected-dealer-id').val(id);
        $('#selected-dealer-name').val(name);
        $('#selected-dealer-subdomain').val(subdomain);

        // Trigger validation update
        updateFieldValidation('dealer', true);

        // Load customers for selected dealer
        loadCustomersForDealer(id);
    }

    function displaySelectedDealer(dealer) {
        $('#dealer-name').text(dealer.name);
        $('#dealer-subdomain').text(dealer.subdomain);
        $('#selected-dealer').show();
        $('#dealer-search').hide();
    }

    function clearDealerSelection() {
        selectedDealer = null;
        $('#selected-dealer').hide();
        $('#dealer-search').show().val('').focus();

        // Clear hidden inputs
        $('#selected-dealer-id').val('');
        $('#selected-dealer-name').val('');
        $('#selected-dealer-subdomain').val('');

        // Clear validation
        updateFieldValidation('dealer', false);

        // Clear customer selection
        clearCustomerSelection();
    }

    function showDealerLoading() {
        $('#dealer-loading').show();
    }

    function hideDealerLoading() {
        $('#dealer-loading').hide();
    }

    function hideDealerResults() {
        $('#dealer-results').hide();
        $('#no-dealer-results').hide();
    }

    function showDealerError(message) {
        $('#dealer-error-message').text(message);
        $('#dealer-error').show();
    }

    function hideDealerError() {
        $('#dealer-error').hide();
    }
</script>
