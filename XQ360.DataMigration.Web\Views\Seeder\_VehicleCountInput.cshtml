@model XQ360.DataMigration.Web.Models.BulkSeederViewModel

<div class="vehicle-count-input">
    <div class="mb-3">
        <input type="number" 
               id="vehicleCount" 
               name="VehicleCount"
               class="form-control" 
               value="@Model.VehicleCount"
               min="1"
               max="1000000"
               placeholder="Enter vehicle count"
               required>
        
        <div id="vehicle-validation-error" class="invalid-feedback" style="display: none;"></div>
        
        <div class="form-text d-flex justify-content-between align-items-center">
            <span id="vehicle-environment-limit" class="text-warning" style="display: none;">
                <i class="fas fa-exclamation-triangle me-1"></i>
                Environment limit: <span id="vehicle-limit-value">0</span>
            </span>
        </div>
    </div>



    <!-- Large Volume Warning -->
    <div id="vehicle-large-volume-warning" class="alert alert-warning" style="display: none;">
        <div class="d-flex align-items-start">
            <i class="fas fa-exclamation-triangle me-2 mt-1"></i>
            <div class="flex-grow-1">
                <div class="fw-bold">Large Volume Operation</div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="vehicle-large-volume-acknowledged">
                    <label class="form-check-label small" for="vehicle-large-volume-acknowledged">
                        I understand this is a large volume operation
                    </label>
                </div>
            </div>
        </div>
    </div>


</div>

<script>
    const vehicleCountConfig = {
        minCount: 1,
        maxCount: 1000000,
        largeVolumeThreshold: 50000,
        environmentLimit: null
    };

    function initializeVehicleCountInput() {
        // Ensure jQuery is available
        if (typeof $ === 'undefined') {
            console.error('jQuery is not loaded - vehicle count input cannot initialize');
            return;
        }
        // Initialize vehicle count validation
        initializeVehicleCountValidation();
        
        // Handle input changes
        $('#vehicleCount').on('input', function() {
            validateVehicleCount();
            updateEstimatedTime();
        });

        $('#vehicleCount').on('blur', function() {
            validateVehicleCount();
        });

        // Handle large volume acknowledgment
        $('#vehicle-large-volume-acknowledged').on('change', function() {
            validateVehicleCount();
        });

        // Initialize with existing value
        if ($('#vehicleCount').val()) {
            validateVehicleCount();
            updateEstimatedTime();
        }
    }

    // Initialize when DOM and scripts are ready
    if (typeof $ !== 'undefined') {
        $(document).ready(function() {
            initializeVehicleCountInput();
        });
    } else {
        // Fallback for when jQuery isn't loaded yet
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof $ !== 'undefined') {
                initializeVehicleCountInput();
            } else {
                // Retry after a short delay
                setTimeout(function() {
                    if (typeof $ !== 'undefined') {
                        initializeVehicleCountInput();
                    }
                }, 100);
            }
        });
    }

    function initializeVehicleCountValidation() {
        $('#vehicle-min-count').text(vehicleCountConfig.minCount.toLocaleString());
        $('#vehicle-max-count').text(vehicleCountConfig.maxCount.toLocaleString());
        
        // TODO: Load environment limits from server
        // loadEnvironmentLimits();
    }

    function validateVehicleCount() {
        const input = $('#vehicleCount');
        const value = parseInt(input.val()) || 0;
        let isValid = true;
        let validationMessage = '';
        let validationTitle = 'Valid Range';
        let validationIcon = '<i class="fas fa-check-circle text-success"></i>';

        // Clear previous validation states
        input.removeClass('is-invalid');
        $('#vehicle-validation-error').hide();
        $('#vehicle-large-volume-warning').hide();

        if (value <= 0) {
            isValid = false;
            validationMessage = 'Vehicle count must be greater than 0';
            validationTitle = 'Invalid Count';
            validationIcon = '<i class="fas fa-exclamation-circle text-danger"></i>';
        } else if (value < vehicleCountConfig.minCount) {
            isValid = false;
            validationMessage = `Minimum vehicle count is ${vehicleCountConfig.minCount.toLocaleString()}`;
            validationTitle = 'Below Minimum';
            validationIcon = '<i class="fas fa-exclamation-triangle text-warning"></i>';
        } else if (value > vehicleCountConfig.maxCount) {
            isValid = false;
            validationMessage = `Maximum vehicle count is ${vehicleCountConfig.maxCount.toLocaleString()}`;
            validationTitle = 'Above Maximum';
            validationIcon = '<i class="fas fa-exclamation-circle text-danger"></i>';
        } else if (vehicleCountConfig.environmentLimit && value > vehicleCountConfig.environmentLimit) {
            isValid = false;
            validationMessage = `Environment limit is ${vehicleCountConfig.environmentLimit.toLocaleString()} vehicles`;
            validationTitle = 'Environment Limit Exceeded';
            validationIcon = '<i class="fas fa-exclamation-triangle text-warning"></i>';
        } else if (value >= vehicleCountConfig.largeVolumeThreshold) {
            // Large volume - check acknowledgment
            $('#vehicle-large-volume-warning').show();
            
            if (!$('#vehicle-large-volume-acknowledged').is(':checked')) {
                isValid = false;
                validationMessage = 'Please acknowledge the large volume operation';
                validationTitle = 'Acknowledgment Required';
                validationIcon = '<i class="fas fa-exclamation-triangle text-warning"></i>';
            } else {
                validationMessage = 'Large volume operation acknowledged';
                validationTitle = 'Large Volume';
                validationIcon = '<i class="fas fa-check-circle text-success"></i>';
            }
        }

        // Show/hide validation feedback
        if (value > 0) {
            $('#vehicle-validation-feedback').show();
            $('#vehicle-requirements').show();
        } else {
            $('#vehicle-validation-feedback').hide();
            $('#vehicle-requirements').hide();
        }

        // Update validation display
        $('#vehicle-validation-icon').html(validationIcon);
        $('#vehicle-validation-title').text(validationTitle);
        $('#vehicle-validation-message').text(validationMessage);

        // Show error state if invalid
        if (!isValid && value > 0) {
            input.addClass('is-invalid');
            $('#vehicle-validation-error').text(validationMessage).show();
        }

        // Update field validation state
        updateFieldValidation('vehicleCount', isValid);

        return isValid;
    }



    function updateEstimatedTime() {
        const vehicleCount = parseInt($('#vehicleCount').val()) || 0;
        
        if (vehicleCount <= 0) {
            $('#vehicle-estimated-time').text('N/A');
            return;
        }

        // Rough estimation: 100 vehicles per minute
        const vehicleMinutes = Math.ceil(vehicleCount / 100);
        const totalMinutes = vehicleMinutes + 1; // Add setup time

        let timeText;
        if (totalMinutes < 60) {
            timeText = `~${totalMinutes} minute${totalMinutes !== 1 ? 's' : ''}`;
        } else {
            const hours = Math.floor(totalMinutes / 60);
            const remainingMinutes = totalMinutes % 60;
            timeText = `~${hours}h ${remainingMinutes}m`;
        }

        $('#vehicle-estimated-time').text(timeText);
    }

    function loadEnvironmentLimits() {
        // TODO: Implement when environment service is available
        $.ajax({
            url: '/api/environment/limits',
            method: 'GET',
            success: function(limits) {
                if (limits.vehicleLimit) {
                    vehicleCountConfig.environmentLimit = limits.vehicleLimit;
                    $('#vehicle-limit-value').text(limits.vehicleLimit.toLocaleString());
                    $('#vehicle-environment-limit').show();
                }
            },
            error: function() {
                // Ignore errors for now
            }
        });
    }

    // Export for use by other components
    window.getVehicleCount = function() {
        return parseInt($('#vehicleCount').val()) || 0;
    };

    window.isVehicleCountValid = function() {
        return validateVehicleCount();
    };
</script>
